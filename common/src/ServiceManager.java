package com.example.common;

import java.util.HashMap;
import java.util.Map;

/**
 * 一个简单的服务管理器，用于注册和获取服务实例
 * (通常在 app 模块中初始化)
 */
public class ServiceManager {
    private static final Map<Class<?>, Object> services = new HashMap<>();

    public static void registerService(Class<?> serviceClass, Object implementation) {
        services.put(serviceClass, implementation);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getService(Class<T> serviceClass) {
        return (T) services.get(serviceClass);
    }
}
