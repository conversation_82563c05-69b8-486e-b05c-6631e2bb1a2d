package com.example.feature_login;

import com.example.common.IUserService;

/**
 * IUserService 的具体实现
 */
public class LoginService implements IUserService {
    private boolean loggedIn = false;
    private String userId = null;

    public void login(String username, String password) {
        // 模拟登录逻辑
        if ("admin".equals(username) && "123456".equals(password)) {
            this.loggedIn = true;
            this.userId = "U1001";
            System.out.println("Login successful!");
        } else {
            System.out.println("Login failed!");
        }
    }

    @Override
    public boolean isLogin() {
        return loggedIn;
    }

    @Override
    public String getUserId() {
        return userId;
    }
}
