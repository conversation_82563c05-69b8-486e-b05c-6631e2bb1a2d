# 模块化架构 Demo 说明

本项目通过一个简化的代码结构，模拟了现代 Android 应用中的**模块化、分层、解耦**的设计思想。它旨在为 `architecture` 目录下的理论文档提供一个具体的实践参考。

## 1. 核心设计思想

本 Demo 主要展示了以下几个核心设计原则：

-   **依赖倒置原则 (DIP)**: 高层模块（如 `feature_profile`）不应该依赖于低层模块（如 `feature_login`）的具体实现，而应该依赖于抽象（`IUserService` 接口）。
-   **单一职责原则 (SRP)**: 每个模块都聚焦于自己的核心业务，如 `feature_login` 只关心登录，`feature_profile` 只关心个人资料。
-   **服务注册与发现**: 通过一个中心的 `ServiceManager` 来管理服务的生命周期和获取，避免模块间的直接引用，实现解耦。

## 2. 模块结构解析

本项目模拟了四个核心模块：

### a. `app` 模块
-   **定位**: **应用主模块/壳工程**。
-   **职责**:
    1.  作为应用的入口 (`MainApplication`)。
    2.  **组装**所有功能模块，管理它们的依赖关系。
    3.  在应用启动时，负责**初始化并注册**所有模块对外提供的服务到 `ServiceManager` 中。
-   **依赖关系**: 依赖所有 `feature_*` 模块和 `common` 模块。

### b. `common` 模块
-   **定位**: **公共基础模块**。
-   **职责**:
    1.  定义所有模块都需要遵守的**公共契约/接口**（如 `IUserService`）。
    2.  提供公共的工具类和服务（如 `ServiceManager`）。
-   **依赖关系**: **不依赖**任何其他模块，它是最底层的模块。

### c. `feature_login` 模块
-   **定位**: **登录业务模块**。
-   **职责**:
    1.  实现 `common` 模块中定义的 `IUserService` 接口，提供具体的登录逻辑 (`LoginService`)。
    2.  包含所有与登录相关的 UI 和业务逻辑。
-   **依赖关系**: 依赖 `common` 模块。

### d. `feature_profile` 模块
-   **定位**: **个人资料业务模块**。
-   **职责**:
    1.  包含所有与用户资料相关的 UI 和业务逻辑 (`ProfileActivity`)。
    2.  通过 `ServiceManager` **获取并使用** `IUserService` 接口来检查登录状态和获取用户信息。
-   **依赖关系**: 依赖 `common` 模块。

## 3. 模块间通信流程

`feature_profile` 模块是如何调用 `feature_login` 模块的功能的？

1.  **定义接口**: 在 `common` 模块中定义 `IUserService` 接口。
2.  **实现服务**: `feature_login` 模块实现 `IUserService` 接口，即 `LoginService`。
3.  **注册服务**: `app` 模块在启动时，创建 `LoginService` 的实例，并将其注册到 `ServiceManager` 中。
4.  **发现并使用服务**: `feature_profile` 模块在需要时，向 `ServiceManager` 请求 `IUserService` 的实例，然后调用其方法。它完全不知道也不关心提供这个服务的具体是哪个模块。

通过这种“接口下沉”和“服务注册”的方式，我们成功地解除了 `feature_profile` 对 `feature_login` 的直接依赖，达到了模块化通信的目的。
